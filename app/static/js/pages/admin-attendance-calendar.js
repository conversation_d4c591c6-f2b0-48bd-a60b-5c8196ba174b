/**
 * Admin Attendance Calendar JavaScript
 * Handles the admin attendance calendar view with employee filtering
 */

// Global variables
let currentYear = new Date().getFullYear();
let currentMonth = new Date().getMonth() + 1;
let selectedEmployeeId = '';
let calendarData = {};

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeCalendar();
    setupEventListeners();
    loadCalendarData();
});

/**
 * Initialize calendar with current date and employee selection
 */
function initializeCalendar() {
    const yearSelect = document.getElementById('yearSelect');
    const monthSelect = document.getElementById('monthSelect');
    const employeeSelect = document.getElementById('employeeSelect');

    if (yearSelect) {
        currentYear = parseInt(yearSelect.value) || currentYear;
    }

    if (monthSelect) {
        currentMonth = parseInt(monthSelect.value) || currentMonth;
    }

    if (employeeSelect) {
        selectedEmployeeId = employeeSelect.value || '';
    }
}

/**
 * Setup event listeners for calendar controls
 */
function setupEventListeners() {
    // Add event listeners for form controls
    const yearSelect = document.getElementById('yearSelect');
    const monthSelect = document.getElementById('monthSelect');
    const employeeSelect = document.getElementById('employeeSelect');

    if (yearSelect) {
        yearSelect.addEventListener('change', updateCalendar);
    }

    if (monthSelect) {
        monthSelect.addEventListener('change', updateCalendar);
    }

    if (employeeSelect) {
        employeeSelect.addEventListener('change', updateCalendar);
    }
}

/**
 * Update calendar when filters change
 */
function updateCalendar() {
    const yearSelect = document.getElementById('yearSelect');
    const monthSelect = document.getElementById('monthSelect');
    const employeeSelect = document.getElementById('employeeSelect');

    if (yearSelect) currentYear = parseInt(yearSelect.value);
    if (monthSelect) currentMonth = parseInt(monthSelect.value);
    if (employeeSelect) selectedEmployeeId = employeeSelect.value;

    loadCalendarData();
}

/**
 * Navigate calendar (previous, current, next)
 */
function navigateCalendar(direction) {
    const monthSelect = document.getElementById('monthSelect');

    if (direction === 'prev') {
        if (currentMonth === 1) {
            currentMonth = 12;
            currentYear--;
        } else {
            currentMonth--;
        }
    } else if (direction === 'next') {
        if (currentMonth === 12) {
            currentMonth = 1;
            currentYear++;
        } else {
            currentMonth++;
        }
    } else if (direction === 'current') {
        const now = new Date();
        currentYear = now.getFullYear();
        currentMonth = now.getMonth() + 1;
    }

    // Update form controls
    document.getElementById('yearSelect').value = currentYear;
    if (monthSelect) {
        monthSelect.value = currentMonth;
    }

    loadCalendarData();
}

/**
 * Refresh calendar data
 */
function refreshCalendar() {
    loadCalendarData();
}

/**
 * Load calendar data from API
 */
function loadCalendarData() {
    const apiUrl = document.getElementById('calendar-api-url').dataset.url;
    const container = document.getElementById('calendarContainer');

    // Show loading state
    container.innerHTML = `
        <div class="text-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p class="text-muted-foreground mt-2">Loading calendar...</p>
        </div>
    `;

    // Build query parameters
    const params = new URLSearchParams({
        year: currentYear,
    });

    if (currentMonth > 0) {
        params.append('month', currentMonth);
    }

    if (selectedEmployeeId) {
        params.append('employee_id', selectedEmployeeId);
    }

    // Fetch calendar data
    fetch(`${apiUrl}?${params.toString()}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            calendarData = data;
            renderCalendar(data);
            updateSummaryPanels(data);
        })
        .catch(error => {
            console.error('Error loading calendar data:', error);
            container.innerHTML = `
                <div class="text-center py-8">
                    <div class="text-destructive">
                        <i data-lucide="alert-circle" class="h-8 w-8 mx-auto mb-2"></i>
                        <p>Error loading calendar data</p>
                        <p class="text-sm text-muted-foreground mt-1">${error.message}</p>
                    </div>
                </div>
            `;

            // Initialize Lucide icons for error display
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        });
}

/**
 * Render calendar with attendance data
 */
function renderCalendar(data) {
    const container = document.getElementById('calendarContainer');
    const year = data.year;
    const month = data.month;

    let calendarHtml = '';

    if (month && month > 0) {
        // Single month view
        calendarHtml = renderMonthCalendar(year, month, data.calendar_data);
    } else {
        // Year view - show all months
        calendarHtml = renderYearCalendar(year, data.calendar_data);
    }

    container.innerHTML = calendarHtml;

    // Initialize Lucide icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
}

/**
 * Render single month calendar
 */
function renderMonthCalendar(year, month, attendanceData) {
    const monthNames = ['', 'January', 'February', 'March', 'April', 'May', 'June',
                       'July', 'August', 'September', 'October', 'November', 'December'];

    const firstDay = new Date(year, month - 1, 1);
    const lastDay = new Date(year, month, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    let html = `
        <div class="calendar-month">
            <div class="grid grid-cols-7 gap-1 mb-2">
                <div class="text-center font-medium text-sm text-muted-foreground p-2">Sun</div>
                <div class="text-center font-medium text-sm text-muted-foreground p-2">Mon</div>
                <div class="text-center font-medium text-sm text-muted-foreground p-2">Tue</div>
                <div class="text-center font-medium text-sm text-muted-foreground p-2">Wed</div>
                <div class="text-center font-medium text-sm text-muted-foreground p-2">Thu</div>
                <div class="text-center font-medium text-sm text-muted-foreground p-2">Fri</div>
                <div class="text-center font-medium text-sm text-muted-foreground p-2">Sat</div>
            </div>
            <div class="grid grid-cols-7 gap-1">
    `;

    // Add empty cells for days before month starts
    for (let i = 0; i < startingDayOfWeek; i++) {
        html += '<div class="calendar-day empty"></div>';
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
        const dateStr = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
        const dayData = attendanceData[dateStr] || [];
        const isToday = isDateToday(year, month, day);

        html += renderCalendarDay(day, dayData, isToday);
    }

    html += '</div></div>';
    return html;
}

/**
 * Render year view with all months
 */
function renderYearCalendar(year, attendanceData) {
    const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
                       'July', 'August', 'September', 'October', 'November', 'December'];

    let html = '<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">';

    for (let month = 1; month <= 12; month++) {
        html += `
            <div class="card">
                <div class="card-header pb-2">
                    <h4 class="card-title text-sm">${monthNames[month - 1]} ${year}</h4>
                </div>
                <div class="card-content pt-0">
                    ${renderMiniMonthCalendar(year, month, attendanceData)}
                </div>
            </div>
        `;
    }

    html += '</div>';
    return html;
}

/**
 * Render mini month calendar for year view
 */
function renderMiniMonthCalendar(year, month, attendanceData) {
    const firstDay = new Date(year, month - 1, 1);
    const lastDay = new Date(year, month, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    let html = `
        <div class="grid grid-cols-7 gap-1 text-xs">
            <div class="text-center font-medium text-muted-foreground p-1">S</div>
            <div class="text-center font-medium text-muted-foreground p-1">M</div>
            <div class="text-center font-medium text-muted-foreground p-1">T</div>
            <div class="text-center font-medium text-muted-foreground p-1">W</div>
            <div class="text-center font-medium text-muted-foreground p-1">T</div>
            <div class="text-center font-medium text-muted-foreground p-1">F</div>
            <div class="text-center font-medium text-muted-foreground p-1">S</div>
    `;

    // Add empty cells for days before month starts
    for (let i = 0; i < startingDayOfWeek; i++) {
        html += '<div class="p-1"></div>';
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
        const dateStr = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
        const dayData = attendanceData[dateStr] || [];
        const isToday = isDateToday(year, month, day);

        html += renderMiniCalendarDay(day, dayData, isToday);
    }

    html += '</div>';
    return html;
}

/**
 * Render individual calendar day
 */
function renderCalendarDay(day, attendanceRecords, isToday) {
    const year = calendarData.year;
    const month = calendarData.month || currentMonth;
    const dateStr = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;

    const hasAttendance = attendanceRecords && attendanceRecords.length > 0;

    // Match user calendar styling exactly
    let dayClass = 'p-2 h-24 border border-gray-200 dark:border-gray-600 relative cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors';

    // Today styling
    if (isToday) {
        dayClass += ' bg-blue-50 dark:bg-blue-900/20 border-blue-300 dark:border-blue-600';
    }

    let indicators = '';
    let tooltipText = `${dateStr}`;

    // Attendance indicators - show status dots and type background
    if (hasAttendance) {
        const statusCounts = {};
        const typeCounts = {};

        attendanceRecords.forEach(record => {
            const status = record.status;
            const typeCode = record.attendance_type?.code;

            statusCounts[status] = (statusCounts[status] || 0) + 1;
            if (typeCode) {
                typeCounts[typeCode] = (typeCounts[typeCode] || 0) + 1;
            }
        });

        // Add status dots (up to 3 statuses)
        const uniqueStatuses = Object.keys(statusCounts);
        uniqueStatuses.slice(0, 3).forEach((status, index) => {
            const statusColor = getStatusColorClass(status);
            const count = statusCounts[status];
            const positions = ['bottom-1 left-1', 'bottom-1 left-4', 'bottom-1 left-7'];
            const position = positions[index] || 'bottom-1 right-1';
            indicators += `<div class="absolute ${position} w-2 h-2 ${statusColor} rounded-full border border-white" title="${count} ${status} record(s)"></div>`;
        });

        // Add type background indicator (most common type)
        const mostCommonType = Object.keys(typeCounts).reduce((a, b) =>
            typeCounts[a] > typeCounts[b] ? a : b, Object.keys(typeCounts)[0]
        );

        if (mostCommonType) {
            const typeColor = getTypeColor(mostCommonType);
            // Add a subtle type background
            dayClass += ` relative overflow-hidden`;
            indicators += `<div class="absolute top-0 right-0 w-3 h-3 opacity-60" style="background-color: ${typeColor};" title="Primary type: ${mostCommonType}"></div>`;
        }

        tooltipText += `\n${attendanceRecords.length} attendance record(s)`;

        // Add employee count indicator in top right
        indicators += `<div class="absolute top-1 right-1 bg-primary text-primary-foreground text-xs rounded-full w-4 h-4 flex items-center justify-center font-medium z-10">${attendanceRecords.length}</div>`;
    }

    return `
        <div class="${dayClass}" onclick="selectDate('${dateStr}')" title="${tooltipText}">
            <div class="text-sm ${isToday ? 'font-bold text-blue-600 dark:text-blue-400' : 'text-gray-900 dark:text-white'}">${day}</div>
            ${indicators}
            ${hasAttendance ? `<div class="text-xs text-gray-600 dark:text-gray-400 mt-1 truncate">${attendanceRecords.length} employee${attendanceRecords.length !== 1 ? 's' : ''}</div>` : ''}
        </div>
    `;
}

/**
 * Render mini calendar day for year view
 */
function renderMiniCalendarDay(day, attendanceRecords, isToday) {
    const todayClass = isToday ? 'bg-primary text-primary-foreground' : '';
    const hasAttendance = attendanceRecords && attendanceRecords.length > 0;
    const attendanceClass = hasAttendance ? 'bg-muted' : '';

    return `
        <div class="p-1 text-center rounded ${todayClass} ${attendanceClass}" title="${hasAttendance ? `${attendanceRecords.length} attendance records` : ''}">
            ${day}
        </div>
    `;
}

/**
 * Get status color class for indicators
 */
function getStatusColorClass(status) {
    const colorMap = {
        'Pending': 'bg-yellow-500',
        'Approved': 'bg-green-500',
        'Rejected': 'bg-red-500',
        'Auto-Approved': 'bg-green-500',
        'Cancelled': 'bg-gray-500'
    };
    return colorMap[status] || 'bg-gray-500';
}

/**
 * Get status color for display
 */
function getStatusColor(status) {
    const colorMap = {
        'Pending': 'bg-yellow-500',
        'Approved': 'bg-green-500',
        'Rejected': 'bg-red-500',
        'Auto-Approved': 'bg-green-500',
        'Cancelled': 'bg-gray-500'
    };
    return colorMap[status] || 'bg-gray-500';
}

/**
 * Get type color for display (using database color codes)
 */
function getTypeColor(typeCode) {
    const colorMap = {
        'WFH': '#9B59B6',    // Purple - Work From Home
        'PVL': '#2ECC71',    // Green - Planned Vacation Leave
        'SL': '#E74C3C',     // Red - Sick Leave
        'EL': '#E74C3C',     // Red - Emergency Leave
        'RTO': '#3498DB',    // Blue - Return to Office
        'HD': '#F1C40F',     // Yellow - Half Day
        'OCW': '#34495E',    // Dark Blue/Grey - On Call Work
        'HW': '#1ABC9C'      // Turquoise - Holiday Work
    };
    return colorMap[typeCode] || '#6B7280'; // Default gray
}

/**
 * Get type color class for Tailwind CSS
 */
function getTypeColorClass(typeCode) {
    const colorMap = {
        'WFH': 'bg-purple-500',    // Work From Home
        'PVL': 'bg-green-500',     // Planned Vacation Leave
        'SL': 'bg-red-500',        // Sick Leave
        'EL': 'bg-red-500',        // Emergency Leave
        'RTO': 'bg-blue-500',      // Return to Office
        'HD': 'bg-yellow-500',     // Half Day
        'OCW': 'bg-gray-700',      // On Call Work
        'HW': 'bg-teal-500'        // Holiday Work
    };
    return colorMap[typeCode] || 'bg-gray-500';
}

/**
 * Select date handler - show modal with employee details
 */
function selectDate(dateStr) {
    const dayData = calendarData.calendar_data[dateStr] || [];

    if (dayData.length === 0) {
        return; // No attendance records for this date
    }

    // Group employees by team
    const employeesByTeam = groupEmployeesByTeam(dayData);

    // Build modal content
    const content = buildDateModalContent(dateStr, employeesByTeam);

    // Show modal
    showDateModal(content);
}

/**
 * Group employees by their teams
 */
function groupEmployeesByTeam(attendanceRecords) {
    const teamGroups = {};

    attendanceRecords.forEach(record => {
        const employeeTeams = record.employee_teams || [];

        if (employeeTeams.length === 0) {
            // Employee not in any team
            if (!teamGroups['No Team']) {
                teamGroups['No Team'] = [];
            }
            teamGroups['No Team'].push(record);
        } else {
            // Employee in one or more teams
            employeeTeams.forEach(team => {
                if (!teamGroups[team.name]) {
                    teamGroups[team.name] = [];
                }
                teamGroups[team.name].push(record);
            });
        }
    });

    return teamGroups;
}

/**
 * Build modal content for selected date
 */
function buildDateModalContent(dateStr, employeesByTeam) {
    const selectedDate = new Date(dateStr);
    const formattedDate = selectedDate.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });

    let content = `<div class="space-y-4">`;

    // Date header
    content += `
        <div class="flex items-center space-x-2 mb-4">
            <i data-lucide="calendar" class="h-5 w-5 text-muted-foreground"></i>
            <span class="font-medium text-lg">${formattedDate}</span>
        </div>`;

    // Team sections
    const teamNames = Object.keys(employeesByTeam).sort();

    teamNames.forEach(teamName => {
        const employees = employeesByTeam[teamName];

        content += `
            <div class="border border-border rounded-lg overflow-hidden">
                <div class="bg-muted/50 px-4 py-2 border-b border-border">
                    <div class="flex items-center justify-between">
                        <h4 class="font-medium text-sm">${teamName}</h4>
                        <span class="text-xs text-muted-foreground">${employees.length} employee${employees.length !== 1 ? 's' : ''}</span>
                    </div>
                </div>
                <div class="p-3 space-y-3">`;

        employees.forEach(record => {
            const statusColor = getStatusColorClass(record.status);
            const statusDisplay = record.status_display || record.status;
            const typeCode = record.attendance_type?.code;
            const typeName = record.attendance_type?.name || 'Unknown Type';
            const typeColor = typeCode ? getTypeColor(typeCode) : '#6B7280';

            content += `
                <div class="flex items-start space-x-3 p-3 rounded-lg bg-background border border-border">
                    <div class="flex items-center mt-1">
                        <div class="w-3 h-3 ${statusColor} rounded-full border border-white shadow-sm" title="Status: ${statusDisplay}"></div>
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center justify-between mb-2">
                            <span class="font-medium text-sm">${record.employee_name}</span>
                            <div class="flex items-center space-x-2">
                                <span class="text-xs px-2 py-1 rounded-full ${statusColor.replace('bg-', 'bg-').replace('-500', '-100')} text-${statusColor.replace('bg-', '').replace('-500', '-800')} border">${statusDisplay}</span>
                            </div>
                        </div>
                        <div class="flex items-center text-sm text-muted-foreground mb-1">
                            <span>${typeName}</span>
                            <div class="w-3 h-3 rounded ml-2 border border-gray-300" style="background-color: ${typeColor};" title="Type: ${typeName}"></div>
                            ${typeCode ? `<span class="ml-2 text-xs font-mono bg-gray-100 px-1 rounded">${typeCode}</span>` : ''}
                        </div>
                        ${record.start_time && record.end_time ? `
                            <div class="text-xs text-muted-foreground flex items-center">
                                <i data-lucide="clock" class="h-3 w-3 mr-1"></i>
                                ${record.start_time} - ${record.end_time}
                            </div>
                        ` : ''}
                        ${record.notes ? `
                            <div class="text-xs text-muted-foreground mt-2 p-2 bg-gray-50 rounded italic border-l-2 border-gray-300">
                                "${record.notes}"
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
        });

        content += `
                </div>
            </div>
        `;
    });

    content += `</div>`;
    return content;
}

/**
 * Show date modal
 */
function showDateModal(content) {
    // Check if modal exists, if not create it
    let modal = document.getElementById('admin-calendar-date-modal');
    if (!modal) {
        // Create modal dynamically
        modal = document.createElement('div');
        modal.id = 'admin-calendar-date-modal';
        modal.className = 'fixed inset-0 z-50 hidden';
        modal.innerHTML = `
            <div class="fixed inset-0 bg-black bg-opacity-50" onclick="closeDateModal()"></div>
            <div class="fixed inset-0 flex items-center justify-center p-4">
                <div class="bg-background border border-border rounded-lg shadow-lg max-w-2xl w-full max-h-[80vh] overflow-hidden">
                    <div class="flex items-center justify-between p-4 border-b border-border">
                        <h3 class="text-lg font-semibold">Attendance Details</h3>
                        <button onclick="closeDateModal()" class="text-muted-foreground hover:text-foreground">
                            <i data-lucide="x" class="h-5 w-5"></i>
                        </button>
                    </div>
                    <div id="admin-calendar-modal-content" class="p-4 overflow-y-auto max-h-[calc(80vh-80px)]">
                        <!-- Content will be populated here -->
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }

    // Update content
    document.getElementById('admin-calendar-modal-content').innerHTML = content;

    // Show modal
    modal.classList.remove('hidden');

    // Initialize Lucide icons in modal
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
}

/**
 * Close date modal
 */
function closeDateModal() {
    const modal = document.getElementById('admin-calendar-date-modal');
    if (modal) {
        modal.classList.add('hidden');
    }
}

// Add global function for closing modal
window.closeDateModal = closeDateModal;

/**
 * Update summary panels with calendar data
 */
function updateSummaryPanels(data) {
    updateRecentAttendance(data);
    updateAttendanceStats(data);
    updateAttendanceCount(data);
    loadAttendanceStatistics();
}

/**
 * Update recent attendance panel
 */
function updateRecentAttendance(data) {
    const container = document.getElementById('recentAttendance');
    if (!container) return;

    const recentRecords = [];

    // Collect all records and sort by date
    Object.entries(data.calendar_data).forEach(([date, records]) => {
        records.forEach(record => {
            recentRecords.push({...record, date});
        });
    });

    // Sort by date descending and take first 5
    recentRecords.sort((a, b) => new Date(b.date) - new Date(a.date));
    const topRecords = recentRecords.slice(0, 5);

    if (topRecords.length === 0) {
        container.innerHTML = '<p class="text-sm text-muted-foreground">No recent attendance records</p>';
        return;
    }

    let html = '';
    topRecords.forEach(record => {
        const statusColor = getStatusColor(record.status);
        const formattedDate = new Date(record.date).toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric'
        });

        html += `
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 rounded-full ${statusColor}"></div>
                    <div>
                        <div class="text-sm font-medium">${record.employee_name}</div>
                        <div class="text-xs text-muted-foreground">${record.attendance_type?.name || 'Unknown'}</div>
                    </div>
                </div>
                <div class="text-xs text-muted-foreground">${formattedDate}</div>
            </div>
        `;
    });

    container.innerHTML = html;
}

/**
 * Update attendance statistics panel
 */
function updateAttendanceStats(data) {
    const container = document.getElementById('attendanceStats');
    if (!container) return;

    const stats = {
        total: 0,
        approved: 0,
        pending: 0,
        rejected: 0,
        employees: new Set()
    };

    // Calculate statistics
    Object.entries(data.calendar_data).forEach(([date, records]) => {
        records.forEach(record => {
            stats.total++;
            stats.employees.add(record.employee_id);

            if (record.status === 'Approved' || record.status === 'Auto-Approved') {
                stats.approved++;
            } else if (record.status === 'Pending') {
                stats.pending++;
            } else if (record.status === 'Rejected') {
                stats.rejected++;
            }
        });
    });

    const html = `
        <div class="space-y-2">
            <div class="flex justify-between">
                <span class="text-sm">Total Records:</span>
                <span class="text-sm font-medium">${stats.total}</span>
            </div>
            <div class="flex justify-between">
                <span class="text-sm">Employees:</span>
                <span class="text-sm font-medium">${stats.employees.size}</span>
            </div>
            <div class="flex justify-between">
                <span class="text-sm text-green-600">Approved:</span>
                <span class="text-sm font-medium">${stats.approved}</span>
            </div>
            <div class="flex justify-between">
                <span class="text-sm text-yellow-600">Pending:</span>
                <span class="text-sm font-medium">${stats.pending}</span>
            </div>
            <div class="flex justify-between">
                <span class="text-sm text-red-600">Rejected:</span>
                <span class="text-sm font-medium">${stats.rejected}</span>
            </div>
        </div>
    `;

    container.innerHTML = html;
}

/**
 * Update attendance count in header
 */
function updateAttendanceCount(data) {
    const container = document.getElementById('attendanceCount');
    if (!container) return;

    let totalRecords = 0;
    Object.values(data.calendar_data).forEach(records => {
        totalRecords += records.length;
    });

    container.textContent = totalRecords;
}

/**
 * Helper function to check if date is today
 */
function isDateToday(year, month, day) {
    const today = new Date();
    return year === today.getFullYear() &&
           month === (today.getMonth() + 1) &&
           day === today.getDate();
}

/**
 * Open add attendance record form (placeholder)
 */
function openAddAttendanceRecordForm() {
    // This would typically open a modal or redirect to add form
    console.log('Opening add attendance record form...');
    // You can implement this based on your existing form handling
}

/**
 * Load comprehensive attendance statistics
 */
function loadAttendanceStatistics() {
    // Build query parameters
    const params = new URLSearchParams({
        year: currentYear,
    });

    if (currentMonth > 0) {
        params.append('month', currentMonth);
    }

    if (selectedEmployeeId) {
        params.append('employee_id', selectedEmployeeId);
    }

    // Fetch statistics data
    fetch(`/api/attendance/admin/statistics?${params.toString()}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                updateStatisticsCards(data.statistics);
            } else {
                console.error('Failed to load statistics:', data.error);
                showStatisticsError(data.error);
            }
        })
        .catch(error => {
            console.error('Error loading statistics:', error);
            showStatisticsError(error.message);
        });
}

/**
 * Update all statistics cards with data
 */
function updateStatisticsCards(stats) {
    updateAttendanceTypeStats(stats.attendance_types || {});
    updateWorkingDaysStats(stats.working_days || {});
    updateFteStats(stats.fte_stats || {});
    updateUnplannedPtoStats(stats.unplanned_pto || {});
    updateAtdPercentageStats(stats.atd_percentage || {});
    updateTrendsInsights(stats);
}

/**
 * Update attendance type statistics card
 */
function updateAttendanceTypeStats(typeStats) {
    const container = document.getElementById('attendanceTypeStats');
    if (!container) return;

    if (Object.keys(typeStats).length === 0) {
        container.innerHTML = '<p class="text-sm text-muted-foreground">No attendance type data</p>';
        return;
    }

    let html = '<div class="space-y-2">';

    // Sort by count descending
    const sortedTypes = Object.entries(typeStats).sort((a, b) => b[1].count - a[1].count);

    sortedTypes.slice(0, 5).forEach(([code, data]) => {
        const color = data.color || '#6B7280';
        html += `
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 rounded" style="background-color: ${color};"></div>
                    <span class="text-xs font-medium">${data.name}</span>
                </div>
                <div class="text-right">
                    <div class="text-xs font-medium">${data.count}</div>
                    <div class="text-xs text-muted-foreground">${data.percentage}%</div>
                </div>
            </div>
        `;
    });

    html += '</div>';
    container.innerHTML = html;
}

/**
 * Update working days statistics card
 */
function updateWorkingDaysStats(workingDaysStats) {
    const container = document.getElementById('workingDaysStats');
    if (!container) return;

    if (Object.keys(workingDaysStats).length === 0) {
        container.innerHTML = '<p class="text-sm text-muted-foreground">No working days data</p>';
        return;
    }

    const html = `
        <div class="space-y-2">
            <div class="flex justify-between">
                <span class="text-xs text-muted-foreground">Working Days:</span>
                <span class="text-xs font-medium">${workingDaysStats.working_days || 0}</span>
            </div>
            <div class="flex justify-between">
                <span class="text-xs text-muted-foreground">Total Days:</span>
                <span class="text-xs font-medium">${workingDaysStats.total_days || 0}</span>
            </div>
            <div class="flex justify-between">
                <span class="text-xs text-muted-foreground">Holidays:</span>
                <span class="text-xs font-medium">${workingDaysStats.holiday_days || 0}</span>
            </div>
            ${workingDaysStats.remaining_working_days > 0 ? `
                <div class="flex justify-between">
                    <span class="text-xs text-muted-foreground">Remaining:</span>
                    <span class="text-xs font-medium">${workingDaysStats.remaining_working_days}</span>
                </div>
            ` : ''}
        </div>
    `;

    container.innerHTML = html;
}

/**
 * Update FTE statistics card
 */
function updateFteStats(fteStats) {
    const container = document.getElementById('fteStats');
    if (!container) return;

    if (Object.keys(fteStats).length === 0) {
        container.innerHTML = '<p class="text-sm text-muted-foreground">No FTE data</p>';
        return;
    }

    const html = `
        <div class="space-y-2">
            <div class="flex justify-between">
                <span class="text-xs text-muted-foreground">Total FTE:</span>
                <span class="text-xs font-medium">${fteStats.total_fte || 0}</span>
            </div>
            <div class="flex justify-between">
                <span class="text-xs text-muted-foreground">With Records:</span>
                <span class="text-xs font-medium">${fteStats.fte_with_attendance || 0}</span>
            </div>
            <div class="flex justify-between">
                <span class="text-xs text-muted-foreground">Rate:</span>
                <span class="text-xs font-medium ${fteStats.attendance_rate >= 90 ? 'text-green-600' : fteStats.attendance_rate >= 70 ? 'text-yellow-600' : 'text-red-600'}">${fteStats.attendance_rate || 0}%</span>
            </div>
        </div>
    `;

    container.innerHTML = html;
}

/**
 * Update unplanned PTO statistics card
 */
function updateUnplannedPtoStats(unplannedStats) {
    const container = document.getElementById('unplannedPtoStats');
    if (!container) return;

    if (Object.keys(unplannedStats).length === 0) {
        container.innerHTML = '<p class="text-sm text-muted-foreground">No unplanned PTO data</p>';
        return;
    }

    const html = `
        <div class="space-y-2">
            <div class="flex justify-between">
                <span class="text-xs text-muted-foreground">Sick Leave:</span>
                <span class="text-xs font-medium">${unplannedStats.sick_leave?.count || 0} (${unplannedStats.sick_leave?.percentage || 0}%)</span>
            </div>
            <div class="flex justify-between">
                <span class="text-xs text-muted-foreground">Emergency:</span>
                <span class="text-xs font-medium">${unplannedStats.emergency_leave?.count || 0} (${unplannedStats.emergency_leave?.percentage || 0}%)</span>
            </div>
            <div class="flex justify-between border-t pt-1">
                <span class="text-xs font-medium">Total:</span>
                <span class="text-xs font-medium ${unplannedStats.total_unplanned?.percentage > 15 ? 'text-red-600' : unplannedStats.total_unplanned?.percentage > 10 ? 'text-yellow-600' : 'text-green-600'}">${unplannedStats.total_unplanned?.count || 0} (${unplannedStats.total_unplanned?.percentage || 0}%)</span>
            </div>
        </div>
    `;

    container.innerHTML = html;
}

/**
 * Update ATD percentage statistics card
 */
function updateAtdPercentageStats(atdStats) {
    const container = document.getElementById('atdPercentageStats');
    if (!container) return;

    if (Object.keys(atdStats).length === 0) {
        container.innerHTML = '<p class="text-sm text-muted-foreground">No ATD data</p>';
        return;
    }

    const rate = atdStats.rate || 0;
    const target = atdStats.target || 95;
    const trend = atdStats.trend || 'stable';

    const rateColor = rate >= target ? 'text-green-600' : rate >= target - 10 ? 'text-yellow-600' : 'text-red-600';
    const trendIcon = trend === 'improving' ? 'trending-up' : trend === 'declining' ? 'trending-down' : 'minus';

    const html = `
        <div class="space-y-2">
            <div class="flex justify-between items-center">
                <span class="text-xs text-muted-foreground">Current Rate:</span>
                <span class="text-xs font-medium ${rateColor}">${rate}%</span>
            </div>
            <div class="flex justify-between">
                <span class="text-xs text-muted-foreground">Target:</span>
                <span class="text-xs font-medium">${target}%</span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-xs text-muted-foreground">Trend:</span>
                <div class="flex items-center space-x-1">
                    <i data-lucide="${trendIcon}" class="h-3 w-3 ${rateColor}"></i>
                    <span class="text-xs font-medium capitalize">${trend}</span>
                </div>
            </div>
        </div>
    `;

    container.innerHTML = html;

    // Initialize Lucide icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
}

/**
 * Update trends and insights card
 */
function updateTrendsInsights(stats) {
    const container = document.getElementById('trendsInsights');
    if (!container) return;

    const overall = stats.overall || {};
    const attendanceTypes = stats.attendance_types || {};
    const unplannedPto = stats.unplanned_pto || {};

    let insights = [];

    // Generate insights based on data
    if (overall.total_records > 0) {
        const participationRate = (overall.unique_employees / overall.total_employees * 100);
        if (participationRate < 50) {
            insights.push({
                type: 'warning',
                text: `Low participation: Only ${Math.round(participationRate)}% of employees have attendance records`
            });
        } else if (participationRate > 90) {
            insights.push({
                type: 'success',
                text: `High participation: ${Math.round(participationRate)}% of employees active`
            });
        }
    }

    // Check for high unplanned PTO
    if (unplannedPto.total_unplanned?.percentage > 15) {
        insights.push({
            type: 'warning',
            text: `High unplanned PTO: ${unplannedPto.total_unplanned.percentage}% of records`
        });
    }

    // Check for most common attendance type
    const sortedTypes = Object.entries(attendanceTypes).sort((a, b) => b[1].count - a[1].count);
    if (sortedTypes.length > 0) {
        const topType = sortedTypes[0][1];
        insights.push({
            type: 'info',
            text: `Most common: ${topType.name} (${topType.percentage}%)`
        });
    }

    if (insights.length === 0) {
        insights.push({
            type: 'info',
            text: 'No significant trends detected'
        });
    }

    let html = '<div class="space-y-2">';
    insights.slice(0, 3).forEach(insight => {
        const iconClass = insight.type === 'warning' ? 'alert-triangle text-yellow-600' :
                         insight.type === 'success' ? 'check-circle text-green-600' :
                         'info text-blue-600';

        html += `
            <div class="flex items-start space-x-2">
                <i data-lucide="${iconClass.split(' ')[0]}" class="h-3 w-3 mt-0.5 ${iconClass.split(' ').slice(1).join(' ')}"></i>
                <span class="text-xs text-muted-foreground">${insight.text}</span>
            </div>
        `;
    });
    html += '</div>';

    container.innerHTML = html;

    // Initialize Lucide icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
}

/**
 * Show statistics error
 */
function showStatisticsError(errorMessage) {
    const containers = [
        'attendanceTypeStats',
        'workingDaysStats',
        'fteStats',
        'unplannedPtoStats',
        'atdPercentageStats',
        'trendsInsights'
    ];

    containers.forEach(containerId => {
        const container = document.getElementById(containerId);
        if (container) {
            container.innerHTML = `
                <div class="text-center py-2">
                    <i data-lucide="alert-circle" class="h-4 w-4 text-destructive mx-auto mb-1"></i>
                    <p class="text-xs text-destructive">Error loading data</p>
                </div>
            `;
        }
    });

    // Initialize Lucide icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
}

/**
 * Refresh statistics manually
 */
function refreshStatistics() {
    loadAttendanceStatistics();
}

/**
 * Export statistics data
 */
function exportStatistics() {
    // Build query parameters
    const params = new URLSearchParams({
        year: currentYear,
    });

    if (currentMonth > 0) {
        params.append('month', currentMonth);
    }

    if (selectedEmployeeId) {
        params.append('employee_id', selectedEmployeeId);
    }

    // Create download link
    const exportUrl = `/api/attendance/admin/statistics/export?${params.toString()}`;

    // Create temporary link and trigger download
    const link = document.createElement('a');
    link.href = exportUrl;
    link.download = `attendance-statistics-${currentYear}${currentMonth > 0 ? '-' + currentMonth.toString().padStart(2, '0') : ''}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}
