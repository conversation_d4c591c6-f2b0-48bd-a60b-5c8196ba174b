{% extends "base.html" %}

{% from "components/page_header.html" import page_header %}
{% from "components/button.html" import button, button_group, icon_button %}
{% from "components/action_buttons.html" import action_buttons %}
{% from "partials/forms/base_form.html" import form_group %}

{# Custom select macro for calendar controls with onchange support #}
{% macro calendar_select(label, name, options, selected_value, onchange=None, min_width="auto") %}
<div class="flex items-center space-x-2">
  <label for="{{ name }}" class="text-sm font-medium whitespace-nowrap">{{ label }}:</label>
  <div class="relative">
    <select id="{{ name }}" name="{{ name }}"
            class="flex h-10 w-auto min-w-[{{ min_width }}] rounded-md border border-input bg-background pl-3 pr-10 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 appearance-none"
            {% if onchange %}onchange="{{ onchange }}"{% endif %}>
      {% for option_value, option_label in options %}
        <option value="{{ option_value }}" {% if selected_value == option_value %}selected{% endif %}>{{ option_label }}</option>
      {% endfor %}
    </select>
    <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
      <i data-lucide="chevron-down" class="h-4 w-4 text-muted-foreground"></i>
    </div>
  </div>
</div>
{% endmacro %}

{% block title %}Attendance Calendar{% endblock %}

{% block header %}Attendance Calendar{% endblock %}

{% block content %}
<!-- Hidden fields for JavaScript -->
<input type="hidden" id="calendar-api-url" data-url="{{ url_for('api.get_admin_attendance_calendar') }}">
<input type="hidden" id="csrf-token" data-token="{{ csrf_token() }}">

<div class="space-y-6">
  {{ page_header(
    title="Attendance Calendar",
    button_text="Add Attendance Record",
    button_icon="plus",
    button_action="openAddAttendanceRecordForm()",
    description="View and manage employee attendance records in calendar format."
  ) }}

  <!-- Filter Controls -->
  <div class="card">
    <div class="card-content p-6">
      <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 sm:gap-6 px-2">
        <!-- View Controls -->
        <div class="flex flex-wrap items-center gap-4">
          <!-- Employee Selector -->
          {% set employee_options = [('', 'All Employees')] %}
          {% for employee in employees %}
            {% set _ = employee_options.append((employee.id, employee.full_name)) %}
          {% endfor %}
          {{ calendar_select("Employee", "employeeSelect", employee_options, selected_employee_id or '', "updateCalendar()", "180px") }}

          <!-- Year Selector -->
          {% set year_options = [] %}
          {% for year in range(current_year - 2, current_year + 3) %}
            {% set _ = year_options.append((year, year)) %}
          {% endfor %}
          {{ calendar_select("Year", "yearSelect", year_options, current_year, "updateCalendar()", "80px") }}

          <!-- Month Selector -->
          {% set month_options = [
            (0, 'All Months'),
            (1, 'January'), (2, 'February'), (3, 'March'), (4, 'April'),
            (5, 'May'), (6, 'June'), (7, 'July'), (8, 'August'),
            (9, 'September'), (10, 'October'), (11, 'November'), (12, 'December')
          ] %}
          {{ calendar_select("Month", "monthSelect", month_options, current_month, "updateCalendar()", "120px") }}
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-wrap gap-2 ml-auto">
          {{ button("List View", variant="outline", href=url_for('admin_attendance.list_attendance_records'), icon="list") }}
          {{ icon_button("refresh-cw", variant="outline", onclick="refreshCalendar()", title="Refresh Calendar") }}
        </div>
      </div>
    </div>
  </div>

  <!-- Calendar Display -->
  <div class="card">
    <div class="card-header">
      <div class="flex items-center justify-between">
        <div>
          <h3 id="calendarTitle" class="card-title">
            {{ current_year }}
            {% if current_month > 0 %}
            - {{ ['', 'January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'][current_month] }}
            {% endif %}
            {% if selected_employee_id %}
            - {{ employees|selectattr('id', 'equalto', selected_employee_id|int)|first|attr('full_name') }}
            {% else %}
            - All Employees
            {% endif %}
          </h3>
          <div class="text-sm text-muted-foreground">
            <span id="attendanceCount">Loading...</span> attendance records found
          </div>
        </div>

        <!-- Navigation Controls -->
        <div class="flex items-center">
          {% set nav_buttons = [
            {"text": "Previous", "variant": "outline", "size": "sm", "onclick": "navigateCalendar('prev')", "icon": "chevron-left"},
            {"text": "This Month", "variant": "outline", "size": "sm", "onclick": "navigateCalendar('current')", "icon": "calendar"},
            {"text": "Next", "variant": "outline", "size": "sm", "onclick": "navigateCalendar('next')", "icon": "chevron-right", "icon_position": "right"}
          ] %}
          {{ button_group(nav_buttons) }}
        </div>
      </div>
    </div>
    <div class="card-content">
      <div id="calendarContainer">
        <!-- Calendar will be rendered here by JavaScript -->
        <div class="text-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p class="text-muted-foreground mt-2">Loading calendar...</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Comprehensive Statistics Dashboard -->
  <div class="space-y-6">
    <!-- Primary Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
      <!-- Attendance Type Statistics -->
      <div class="card">
        <div class="card-header">
          <div class="flex items-center justify-between">
            <h3 class="card-title text-sm">Attendance Types</h3>
            <i data-lucide="pie-chart" class="h-4 w-4 text-muted-foreground"></i>
          </div>
        </div>
        <div class="card-content">
          <div id="attendanceTypeStats" class="space-y-2">
            <!-- Loading state -->
            <div class="animate-pulse space-y-2">
              <div class="h-3 bg-muted rounded"></div>
              <div class="h-3 bg-muted rounded w-3/4"></div>
              <div class="h-3 bg-muted rounded w-1/2"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Working Days Statistics -->
      <div class="card">
        <div class="card-header">
          <div class="flex items-center justify-between">
            <h3 class="card-title text-sm">Working Days</h3>
            <i data-lucide="calendar-days" class="h-4 w-4 text-muted-foreground"></i>
          </div>
        </div>
        <div class="card-content">
          <div id="workingDaysStats" class="space-y-2">
            <!-- Loading state -->
            <div class="animate-pulse space-y-2">
              <div class="h-3 bg-muted rounded"></div>
              <div class="h-3 bg-muted rounded w-2/3"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- FTE Statistics -->
      <div class="card">
        <div class="card-header">
          <div class="flex items-center justify-between">
            <h3 class="card-title text-sm">FTE Overview</h3>
            <i data-lucide="users" class="h-4 w-4 text-muted-foreground"></i>
          </div>
        </div>
        <div class="card-content">
          <div id="fteStats" class="space-y-2">
            <!-- Loading state -->
            <div class="animate-pulse space-y-2">
              <div class="h-3 bg-muted rounded"></div>
              <div class="h-3 bg-muted rounded w-4/5"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unplanned PTO Statistics -->
      <div class="card">
        <div class="card-header">
          <div class="flex items-center justify-between">
            <h3 class="card-title text-sm">Unplanned PTO</h3>
            <i data-lucide="alert-triangle" class="h-4 w-4 text-muted-foreground"></i>
          </div>
        </div>
        <div class="card-content">
          <div id="unplannedPtoStats" class="space-y-2">
            <!-- Loading state -->
            <div class="animate-pulse space-y-2">
              <div class="h-3 bg-muted rounded"></div>
              <div class="h-3 bg-muted rounded w-3/5"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- ATD Percentage -->
      <div class="card">
        <div class="card-header">
          <div class="flex items-center justify-between">
            <h3 class="card-title text-sm">ATD Rate</h3>
            <i data-lucide="trending-up" class="h-4 w-4 text-muted-foreground"></i>
          </div>
        </div>
        <div class="card-content">
          <div id="atdPercentageStats" class="space-y-2">
            <!-- Loading state -->
            <div class="animate-pulse space-y-2">
              <div class="h-3 bg-muted rounded"></div>
              <div class="h-3 bg-muted rounded w-2/3"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Secondary Statistics and Details -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <!-- Recent Attendance -->
      <div class="card">
        <div class="card-header">
          <div class="flex items-center justify-between">
            <h3 class="card-title">Recent Activity</h3>
            <button onclick="exportStatistics()" class="text-xs text-muted-foreground hover:text-foreground" title="Export Statistics">
              <i data-lucide="download" class="h-4 w-4"></i>
            </button>
          </div>
        </div>
        <div class="card-content">
          <div id="recentAttendance" class="space-y-3">
            <!-- Will be populated by JavaScript -->
          </div>
        </div>
      </div>

      <!-- Detailed Statistics -->
      <div class="card">
        <div class="card-header">
          <div class="flex items-center justify-between">
            <h3 class="card-title">Overall Statistics</h3>
            <button onclick="refreshStatistics()" class="text-xs text-muted-foreground hover:text-foreground" title="Refresh Statistics">
              <i data-lucide="refresh-cw" class="h-4 w-4"></i>
            </button>
          </div>
        </div>
        <div class="card-content">
          <div id="attendanceStats" class="space-y-3">
            <!-- Will be populated by JavaScript -->
          </div>
        </div>
      </div>

      <!-- Trends and Insights -->
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">Trends & Insights</h3>
        </div>
        <div class="card-content">
          <div id="trendsInsights" class="space-y-3">
            <!-- Will be populated by JavaScript -->
          </div>
        </div>
      </div>
    </div>
  </div>

    <!-- Legend -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Legend</h3>
      </div>
      <div class="card-content">
        <div class="space-y-4">
          <!-- Status Legend -->
          <div>
            <h4 class="text-sm font-semibold mb-2 text-muted-foreground">Approval Status</h4>
            <div class="space-y-2">
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                <span class="text-sm">Approved</span>
              </div>
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <span class="text-sm">Pending</span>
              </div>
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                <span class="text-sm">Rejected</span>
              </div>
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-gray-500 rounded-full"></div>
                <span class="text-sm">Cancelled</span>
              </div>
            </div>
          </div>

          <!-- Type Legend -->
          <div>
            <h4 class="text-sm font-semibold mb-2 text-muted-foreground">Attendance Types</h4>
            <div class="space-y-2">
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 rounded" style="background-color: #9B59B6;"></div>
                <span class="text-sm">Work From Home</span>
              </div>
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 rounded" style="background-color: #2ECC71;"></div>
                <span class="text-sm">Planned Vacation Leave</span>
              </div>
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 rounded" style="background-color: #E74C3C;"></div>
                <span class="text-sm">Sick Leave</span>
              </div>
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 rounded" style="background-color: #3498DB;"></div>
                <span class="text-sm">Return to Office</span>
              </div>
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 rounded" style="background-color: #F1C40F;"></div>
                <span class="text-sm">Half Day</span>
              </div>
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 rounded" style="background-color: #34495E;"></div>
                <span class="text-sm">On Call Work</span>
              </div>
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 rounded" style="background-color: #1ABC9C;"></div>
                <span class="text-sm">Holiday Work</span>
              </div>
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 rounded" style="background-color: #E74C3C;"></div>
                <span class="text-sm">Emergency Leave</span>
              </div>
            </div>
          </div>

          <!-- Visual Guide -->
          <div class="pt-2 border-t">
            <p class="text-xs text-muted-foreground">
              Calendar days show status dots (●) and type backgrounds (■)
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

{% endblock %}

{% block scripts %}
{{ super() }}
<script src="{{ url_for('static', filename='js/pages/admin-attendance-calendar.js') }}"></script>
{% endblock %}
